"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Mic, MicOff, Phone, PhoneOff, Volume2 } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface VapiMessage {
  role: "user" | "assistant"
  content: string
  timestamp: string
}

interface VapiVoiceAssistantProps {
  onTranscriptUpdate?: (transcript: string) => void
  medicalContext?: string
  patientId?: string
}

export default function VapiVoiceAssistant({
  onTranscriptUpdate,
  medicalContext = "general",
  patientId,
}: VapiVoiceAssistantProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [messages, setMessages] = useState<VapiMessage[]>([])
  const [currentTranscript, setCurrentTranscript] = useState("")
  const [callDuration, setCallDuration] = useState(0)
  const [connectionStatus, setConnectionStatus] = useState<"disconnected" | "connecting" | "connected" | "error">(
    "disconnected",
  )

  const vapiRef = useRef<any>(null)
  const callStartTimeRef = useRef<number>(0)
  const durationIntervalRef = useRef<NodeJS.Timeout>()

  // VAPI public key (safe for client-side use)
  const VAPI_PUBLIC_KEY = "656db409-57c6-4682-a359-6f469b8f2e15"

  const assistantConfig = {
    model: {
      provider: "openai",
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are a medical voice assistant helping doctors with patient consultations and medical dictation. 
          Context: ${medicalContext}
          ${patientId ? `Patient ID: ${patientId}` : ""}
          
          Guidelines:
          - Provide clear, concise medical information
          - Ask clarifying questions when needed
          - Maintain professional medical terminology
          - Always remind users to verify information with current medical literature
          - Keep responses conversational but informative
          - If asked about specific patient data, indicate you need access to medical records`,
        },
      ],
    },
    voice: {
      provider: "11labs",
      voiceId: "sarah", // Professional female voice
      stability: 0.5,
      similarityBoost: 0.8,
      style: 0.2,
      useSpeakerBoost: true,
    },
    firstMessage: "Hello! I'm your medical voice assistant. How can I help you today?",
    transcriber: {
      provider: "deepgram",
      model: "nova-2-medical",
      language: "en-US",
      smartFormat: true,
      keywords: ["medical", "patient", "diagnosis", "treatment", "medication", "symptoms"],
    },
    endCallMessage: "Thank you for using the medical voice assistant. Have a great day!",
    recordingEnabled: true,
    silenceTimeoutSeconds: 30,
    maxDurationSeconds: 1800, // 30 minutes max
  }

  // Initialize VAPI
  useEffect(() => {
    const initVapi = async () => {
      try {
        // Load VAPI SDK
        const script = document.createElement("script")
        script.src = "https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.js"
        script.onload = () => {
          // @ts-ignore
          if (window.Vapi) {
            // @ts-ignore
            vapiRef.current = new window.Vapi(VAPI_PUBLIC_KEY)
            setupVapiEventListeners()
            console.log("[v0] VAPI initialized successfully")
          }
        }
        script.onerror = () => {
          console.error("[v0] Failed to load VAPI SDK")
          setConnectionStatus("error")
        }
        document.head.appendChild(script)
      } catch (error) {
        console.error("Failed to initialize VAPI:", error)
        setConnectionStatus("error")
      }
    }

    initVapi()

    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current)
      }
      if (vapiRef.current) {
        vapiRef.current.stop()
      }
    }
  }, [])

  // Setup VAPI event listeners
  const setupVapiEventListeners = () => {
    if (!vapiRef.current) return

    vapiRef.current.on("call-start", () => {
      console.log("[v0] VAPI call started")
      setIsConnected(true)
      setConnectionStatus("connected")
      callStartTimeRef.current = Date.now()

      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTimeRef.current) / 1000))
      }, 1000)

      toast({
        title: "Voice Assistant Connected",
        description: "You can now speak with the medical AI assistant",
      })
    })

    vapiRef.current.on("call-end", () => {
      console.log("[v0] VAPI call ended")
      setIsConnected(false)
      setIsListening(false)
      setIsSpeaking(false)
      setConnectionStatus("disconnected")

      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current)
      }

      setCallDuration(0)

      toast({
        title: "Voice Assistant Disconnected",
        description: "The voice session has ended",
      })
    })

    vapiRef.current.on("speech-start", () => {
      console.log("[v0] User started speaking")
      setIsListening(true)
      setIsSpeaking(false)
    })

    vapiRef.current.on("speech-end", () => {
      console.log("[v0] User stopped speaking")
      setIsListening(false)
    })

    vapiRef.current.on("message", (message: any) => {
      console.log("[v0] VAPI message:", message)

      if (message.type === "transcript" && message.transcriptType === "final") {
        const newMessage: VapiMessage = {
          role: message.role === "user" ? "user" : "assistant",
          content: message.transcript,
          timestamp: new Date().toISOString(),
        }

        setMessages((prev) => [...prev, newMessage])
        setCurrentTranscript(message.transcript)

        if (onTranscriptUpdate) {
          onTranscriptUpdate(message.transcript)
        }
      }

      if (message.type === "function-call") {
        // Handle function calls if needed
        console.log("[v0] Function call:", message)
      }
    })

    vapiRef.current.on("error", (error: any) => {
      console.error("[v0] VAPI error:", error)
      setConnectionStatus("error")
      toast({
        title: "Voice Assistant Error",
        description: "There was an issue with the voice connection",
        variant: "destructive",
      })
    })
  }

  // Start voice call
  const startCall = async () => {
    if (!vapiRef.current) {
      toast({
        title: "Error",
        description: "Voice assistant not initialized. Please refresh the page and try again.",
        variant: "destructive",
      })
      return
    }

    try {
      setConnectionStatus("connecting")
      console.log("[v0] Starting VAPI call with config:", assistantConfig)
      await vapiRef.current.start(assistantConfig)
    } catch (error) {
      console.error("Failed to start VAPI call:", error)
      setConnectionStatus("error")
      toast({
        title: "Error",
        description: "Failed to start voice session. Please check your microphone permissions.",
        variant: "destructive",
      })
    }
  }

  // End voice call
  const endCall = () => {
    if (vapiRef.current) {
      vapiRef.current.stop()
    }
  }

  // Toggle mute
  const toggleMute = () => {
    if (vapiRef.current) {
      vapiRef.current.setMuted(!isListening)
    }
  }

  // Format duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              VAPI Voice Assistant
            </CardTitle>
            <CardDescription>AI-powered medical voice consultation and dictation</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant={
                connectionStatus === "connected"
                  ? "default"
                  : connectionStatus === "error"
                    ? "destructive"
                    : "secondary"
              }
            >
              {connectionStatus}
            </Badge>
            {isConnected && <Badge variant="outline">{formatDuration(callDuration)}</Badge>}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Call Controls */}
        <div className="flex items-center justify-center gap-4">
          {!isConnected ? (
            <Button
              onClick={startCall}
              disabled={connectionStatus === "connecting"}
              className="bg-green-600 hover:bg-green-700"
            >
              <Phone className="h-4 w-4 mr-2" />
              {connectionStatus === "connecting" ? "Connecting..." : "Start Voice Session"}
            </Button>
          ) : (
            <>
              <Button onClick={toggleMute} variant={isListening ? "default" : "secondary"}>
                {isListening ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
              </Button>

              <Button onClick={endCall} variant="destructive">
                <PhoneOff className="h-4 w-4 mr-2" />
                End Session
              </Button>
            </>
          )}
        </div>

        {/* Status Indicators */}
        {isConnected && (
          <div className="flex items-center justify-center gap-4 text-sm">
            <div className={`flex items-center gap-2 ${isListening ? "text-green-600" : "text-muted-foreground"}`}>
              <div className={`w-2 h-2 rounded-full ${isListening ? "bg-green-600 animate-pulse" : "bg-gray-400"}`} />
              {isListening ? "Listening..." : "Ready to listen"}
            </div>
            <div className={`flex items-center gap-2 ${isSpeaking ? "text-blue-600" : "text-muted-foreground"}`}>
              <div className={`w-2 h-2 rounded-full ${isSpeaking ? "bg-blue-600 animate-pulse" : "bg-gray-400"}`} />
              {isSpeaking ? "AI Speaking..." : "AI Ready"}
            </div>
          </div>
        )}

        {/* Live Transcript */}
        {messages.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Live Transcript</h4>
            <ScrollArea className="h-48 border rounded-lg p-3">
              <div className="space-y-2">
                {messages.map((message, index) => (
                  <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                    <div
                      className={`max-w-[80%] rounded-lg px-3 py-2 text-sm ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-xs">{message.role === "user" ? "You" : "AI Assistant"}</span>
                        <span className="text-xs opacity-70">
                          {new Date(message.timestamp).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                      <p>{message.content}</p>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Instructions */}
        {!isConnected && (
          <div className="text-center text-sm text-muted-foreground space-y-2">
            <p>Click "Start Voice Session" to begin talking with the AI medical assistant.</p>
            <p>The assistant can help with:</p>
            <ul className="text-xs space-y-1">
              <li>• Medical consultations and advice</li>
              <li>• Clinical decision support</li>
              <li>• Medical dictation and note-taking</li>
              <li>• Patient information queries</li>
            </ul>
            {connectionStatus === "error" && (
              <p className="text-red-600 mt-2">
                ⚠️ Connection error. Please refresh the page and ensure microphone permissions are granted.
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
