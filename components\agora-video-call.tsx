"use client"

import { useEffect, useRef, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Video, VideoOff, Mic, Mic<PERSON>ff, PhoneOff, Share, Co<PERSON>, TestTube } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { API_CONFIG } from "@/lib/config"

interface AgoraVideoCallProps {
  channelName: string
  onCallEnd: () => void
  patientName: string
}

const AGORA_CONFIG = {
  appId: API_CONFIG.AGORA_APP_ID,
  // In production, you would get this token from your backend
  token: null, // Will be generated server-side in production
}

export function AgoraVideoCall({ channelName, onCallEnd, patientName }: AgoraVideoCallProps) {
  const [isVideoEnabled, setIsVideoEnabled] = useState(true)
  const [isAudioEnabled, setIsAudioEnabled] = useState(true)
  const [isConnected, setIsConnected] = useState(false)
  const [agoraClient, setAgoraClient] = useState<any>(null)
  const [localVideoTrack, setLocalVideoTrack] = useState<any>(null)
  const [localAudioTrack, setLocalAudioTrack] = useState<any>(null)
  const [remoteUsers, setRemoteUsers] = useState<any[]>([])
  const [callDuration, setCallDuration] = useState(0)
  const [isTestMode, setIsTestMode] = useState(false)
  const [shareableLink, setShareableLink] = useState("")
  const [connectionQuality, setConnectionQuality] = useState<"excellent" | "good" | "poor" | "unknown">("unknown")

  const localVideoRef = useRef<HTMLDivElement>(null)
  const remoteVideoRef = useRef<HTMLDivElement>(null)
  const callStartTimeRef = useRef<number>(0)
  const durationIntervalRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    // Generate shareable link
    const baseUrl = window.location.origin
    const link = `${baseUrl}/video-calls/join/${channelName}`
    setShareableLink(link)
  }, [channelName])

  useEffect(() => {
    // Initialize Agora client
    const initializeAgora = async () => {
      try {
        console.log("[v0] Initializing Agora with App ID:", AGORA_CONFIG.appId)

        // Dynamically import Agora SDK
        const AgoraRTC = (await import("agora-rtc-sdk-ng")).default

        const client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" })
        setAgoraClient(client)

        client.on("connection-state-change", (curState: string, revState: string) => {
          console.log("[v0] Connection state changed:", curState, "from", revState)
          if (curState === "CONNECTED") {
            setIsConnected(true)
            callStartTimeRef.current = Date.now()

            // Start duration timer
            durationIntervalRef.current = setInterval(() => {
              setCallDuration(Math.floor((Date.now() - callStartTimeRef.current) / 1000))
            }, 1000)
          } else if (curState === "DISCONNECTED") {
            setIsConnected(false)
            if (durationIntervalRef.current) {
              clearInterval(durationIntervalRef.current)
            }
          }
        })

        client.on("network-quality", (stats: any) => {
          if (stats.uplinkNetworkQuality >= 4) {
            setConnectionQuality("excellent")
          } else if (stats.uplinkNetworkQuality >= 3) {
            setConnectionQuality("good")
          } else {
            setConnectionQuality("poor")
          }
        })

        // Create local tracks with enhanced settings
        const [videoTrack, audioTrack] = await AgoraRTC.createMicrophoneAndCameraTracks(
          {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
          {
            optimizationMode: "detail",
            encoderConfig: {
              width: 640,
              height: 480,
              frameRate: 30,
              bitrateMin: 400,
              bitrateMax: 1000,
            },
          },
        )

        setLocalVideoTrack(videoTrack)
        setLocalAudioTrack(audioTrack)

        // Play local video
        if (localVideoRef.current) {
          videoTrack.play(localVideoRef.current)
        }

        // Set up event listeners
        client.on("user-published", async (user: any, mediaType: string) => {
          console.log("[v0] User published:", user.uid, mediaType)
          await client.subscribe(user, mediaType)

          if (mediaType === "video") {
            setRemoteUsers((prev) => [...prev.filter((u) => u.uid !== user.uid), user])
            if (remoteVideoRef.current) {
              user.videoTrack?.play(remoteVideoRef.current)
            }
          }

          if (mediaType === "audio") {
            user.audioTrack?.play()
          }
        })

        client.on("user-unpublished", (user: any) => {
          console.log("[v0] User unpublished:", user.uid)
          setRemoteUsers((prev) => prev.filter((u) => u.uid !== user.uid))
        })

        client.on("user-left", (user: any) => {
          console.log("[v0] User left:", user.uid)
          setRemoteUsers((prev) => prev.filter((u) => u.uid !== user.uid))
        })

        // Join channel
        console.log("[v0] Joining channel:", channelName)
        await client.join(AGORA_CONFIG.appId, channelName, AGORA_CONFIG.token, null)
        await client.publish([videoTrack, audioTrack])

        console.log("[v0] Agora video call initialized successfully")

        toast({
          title: "Video Call Connected",
          description: "Successfully connected to the video call",
        })
      } catch (error) {
        console.error("[v0] Error initializing Agora:", error)
        toast({
          title: "Connection Error",
          description: "Failed to connect to video call. Please check your camera and microphone permissions.",
          variant: "destructive",
        })
      }
    }

    initializeAgora()

    // Cleanup on unmount
    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current)
      }
      if (agoraClient) {
        agoraClient.leave()
        localVideoTrack?.close()
        localAudioTrack?.close()
      }
    }
  }, [channelName])

  const toggleVideo = async () => {
    if (localVideoTrack) {
      await localVideoTrack.setEnabled(!isVideoEnabled)
      setIsVideoEnabled(!isVideoEnabled)
      console.log("[v0] Video toggled:", !isVideoEnabled)
    }
  }

  const toggleAudio = async () => {
    if (localAudioTrack) {
      await localAudioTrack.setEnabled(!isAudioEnabled)
      setIsAudioEnabled(!isAudioEnabled)
      console.log("[v0] Audio toggled:", !isAudioEnabled)
    }
  }

  const endCall = async () => {
    console.log("[v0] Ending call")
    if (agoraClient) {
      await agoraClient.leave()
      localVideoTrack?.close()
      localAudioTrack?.close()
    }
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current)
    }
    onCallEnd()
  }

  const toggleTestMode = () => {
    setIsTestMode(!isTestMode)
    if (!isTestMode) {
      toast({
        title: "Test Mode Enabled",
        description: "You can now test your camera and microphone",
      })
    }
  }

  const copyShareableLink = () => {
    navigator.clipboard.writeText(shareableLink)
    toast({
      title: "Link Copied",
      description: "Video call link copied to clipboard",
    })
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case "excellent":
        return "text-green-600"
      case "good":
        return "text-yellow-600"
      case "poor":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            Video Call with {patientName}
          </CardTitle>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`} />
              <span className="text-sm text-muted-foreground">{isConnected ? "Connected" : "Connecting..."}</span>
              {isConnected && (
                <Badge variant="outline" className={getQualityColor(connectionQuality)}>
                  {connectionQuality}
                </Badge>
              )}
            </div>
            {isConnected && <Badge variant="outline">{formatDuration(callDuration)}</Badge>}
            <Button variant="outline" size="sm" onClick={toggleTestMode}>
              <TestTube className="h-4 w-4 mr-2" />
              {isTestMode ? "Exit Test" : "Test Mode"}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {isTestMode && (
          <Card className="border-blue-200 dark:border-blue-800">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                Camera & Microphone Test
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Camera Test</Label>
                  <div className="mt-2 p-4 border rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      {isVideoEnabled ? "✅ Camera is working" : "❌ Camera is disabled"}
                    </p>
                  </div>
                </div>
                <div>
                  <Label>Microphone Test</Label>
                  <div className="mt-2 p-4 border rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      {isAudioEnabled ? "✅ Microphone is working" : "❌ Microphone is disabled"}
                    </p>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Share Video Call Link</Label>
                <div className="flex gap-2">
                  <Input value={shareableLink} readOnly className="flex-1" />
                  <Button variant="outline" size="icon" onClick={copyShareableLink}>
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Share className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">Share this link with the patient to join the video call</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Video Container */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Local Video */}
          <div className="relative">
            <div
              ref={localVideoRef}
              className="w-full h-80 bg-gray-900 rounded-lg overflow-hidden border-2 border-primary/20"
            />
            <div className="absolute bottom-3 left-3 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
              You (Doctor)
            </div>
            {!isVideoEnabled && (
              <div className="absolute inset-0 bg-gray-900 rounded-lg flex items-center justify-center">
                <div className="text-center text-white">
                  <VideoOff className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Camera Off</p>
                </div>
              </div>
            )}
          </div>

          {/* Remote Video */}
          <div className="relative">
            <div
              ref={remoteVideoRef}
              className="w-full h-80 bg-gray-900 rounded-lg overflow-hidden border-2 border-green-500/20 flex items-center justify-center"
            >
              {remoteUsers.length === 0 && (
                <div className="text-white text-center">
                  <Video className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Waiting for {patientName}</p>
                  <p className="text-sm opacity-75 mt-2">
                    Share the call link or ask them to join channel: {channelName}
                  </p>
                </div>
              )}
            </div>
            {remoteUsers.length > 0 && (
              <div className="absolute bottom-3 left-3 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
                {patientName}
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Call Controls */}
        <div className="flex justify-center gap-4">
          <Button
            variant={isVideoEnabled ? "default" : "destructive"}
            size="lg"
            onClick={toggleVideo}
            className="rounded-full px-6"
          >
            {isVideoEnabled ? <Video className="h-5 w-5 mr-2" /> : <VideoOff className="h-5 w-5 mr-2" />}
            {isVideoEnabled ? "Camera On" : "Camera Off"}
          </Button>

          <Button
            variant={isAudioEnabled ? "default" : "destructive"}
            size="lg"
            onClick={toggleAudio}
            className="rounded-full px-6"
          >
            {isAudioEnabled ? <Mic className="h-5 w-5 mr-2" /> : <MicOff className="h-5 w-5 mr-2" />}
            {isAudioEnabled ? "Mic On" : "Mic Off"}
          </Button>

          <Button variant="destructive" size="lg" onClick={endCall} className="rounded-full px-6">
            <PhoneOff className="h-5 w-5 mr-2" />
            End Call
          </Button>
        </div>

        {/* Call Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center text-sm">
          <div className="p-3 bg-accent rounded-lg">
            <p className="font-medium">Channel</p>
            <p className="text-muted-foreground truncate">{channelName}</p>
          </div>
          <div className="p-3 bg-accent rounded-lg">
            <p className="font-medium">Remote Users</p>
            <p className="text-muted-foreground">{remoteUsers.length}</p>
          </div>
          <div className="p-3 bg-accent rounded-lg">
            <p className="font-medium">Connection</p>
            <p className={`capitalize ${getQualityColor(connectionQuality)}`}>{connectionQuality}</p>
          </div>
          <div className="p-3 bg-accent rounded-lg">
            <p className="font-medium">Duration</p>
            <p className="text-muted-foreground">{formatDuration(callDuration)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
