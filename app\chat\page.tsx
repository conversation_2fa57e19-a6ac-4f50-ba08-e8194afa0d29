"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { MessageSquare, Send, Search, Phone, Video, MoreHorizontal, Paperclip } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface ChatMessage {
  id: string
  sender_type: "doctor" | "patient"
  message: string
  message_type: "text" | "image" | "file" | "voice"
  attachment_url: string | null
  is_read: boolean
  created_at: string
  patient: {
    id: string
    first_name: string
    last_name: string
  }
}

interface Patient {
  id: string
  first_name: string
  last_name: string
  email: string | null
  phone: string | null
}

interface ChatConversation {
  patient: Patient
  lastMessage: ChatMessage | null
  unreadCount: number
}

export default function ChatPage() {
  const [conversations, setConversations] = useState<ChatConversation[]>([])
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const supabase = createClient()

  // Fetch conversations (patients with messages)
  const fetchConversations = async () => {
    try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) throw new Error("Not authenticated")

      // Get all patients
      const { data: patients, error: patientsError } = await supabase
        .from("patients")
        .select("id, first_name, last_name, email, phone")

      if (patientsError) throw patientsError

      // Get last message and unread count for each patient
      const conversationsData: ChatConversation[] = []

      for (const patient of patients || []) {
        // Get last message
        const { data: lastMessage } = await supabase
          .from("chat_messages")
          .select("*")
          .eq("doctor_id", userData.user.id)
          .eq("patient_id", patient.id)
          .order("created_at", { ascending: false })
          .limit(1)
          .single()

        // Get unread count
        const { count: unreadCount } = await supabase
          .from("chat_messages")
          .select("*", { count: "exact", head: true })
          .eq("doctor_id", userData.user.id)
          .eq("patient_id", patient.id)
          .eq("sender_type", "patient")
          .eq("is_read", false)

        conversationsData.push({
          patient,
          lastMessage: lastMessage
            ? {
                ...lastMessage,
                patient: {
                  id: patient.id,
                  first_name: patient.first_name,
                  last_name: patient.last_name,
                },
              }
            : null,
          unreadCount: unreadCount || 0,
        })
      }

      // Sort by last message time
      conversationsData.sort((a, b) => {
        if (!a.lastMessage && !b.lastMessage) return 0
        if (!a.lastMessage) return 1
        if (!b.lastMessage) return -1
        return new Date(b.lastMessage.created_at).getTime() - new Date(a.lastMessage.created_at).getTime()
      })

      setConversations(conversationsData)
    } catch (error) {
      console.error("Error fetching conversations:", error)
      toast({
        title: "Error",
        description: "Failed to fetch conversations",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch messages for selected patient
  const fetchMessages = async (patientId: string) => {
    try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) throw new Error("Not authenticated")

      const { data, error } = await supabase
        .from("chat_messages")
        .select(
          `
          *,
          patient:patients(id, first_name, last_name)
        `,
        )
        .eq("doctor_id", userData.user.id)
        .eq("patient_id", patientId)
        .order("created_at", { ascending: true })

      if (error) throw error

      setMessages(data || [])

      // Mark messages as read
      await supabase
        .from("chat_messages")
        .update({ is_read: true })
        .eq("doctor_id", userData.user.id)
        .eq("patient_id", patientId)
        .eq("sender_type", "patient")
        .eq("is_read", false)

      // Refresh conversations to update unread count
      fetchConversations()
    } catch (error) {
      console.error("Error fetching messages:", error)
      toast({
        title: "Error",
        description: "Failed to fetch messages",
        variant: "destructive",
      })
    }
  }

  // Send message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || !selectedPatient || isSending) return

    setIsSending(true)
    try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) throw new Error("Not authenticated")

      const { error } = await supabase.from("chat_messages").insert({
        doctor_id: userData.user.id,
        patient_id: selectedPatient.id,
        sender_type: "doctor",
        message: newMessage.trim(),
        message_type: "text",
        is_read: false,
      })

      if (error) throw error

      setNewMessage("")
      fetchMessages(selectedPatient.id)
      fetchConversations()
    } catch (error) {
      console.error("Error sending message:", error)
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      })
    } finally {
      setIsSending(false)
    }
  }

  // Select patient conversation
  const selectPatient = (patient: Patient) => {
    setSelectedPatient(patient)
    fetchMessages(patient.id)
  }

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    fetchConversations()
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Filter conversations
  const filteredConversations = conversations.filter((conversation) =>
    `${conversation.patient.first_name} ${conversation.patient.last_name}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase()),
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading conversations...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex gap-6">
      {/* Conversations Sidebar */}
      <Card className="w-80 flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Messages
          </CardTitle>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search conversations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardHeader>
        <CardContent className="flex-1 p-0">
          <ScrollArea className="h-full">
            <div className="space-y-1 p-3">
              {filteredConversations.map((conversation) => (
                <div
                  key={conversation.patient.id}
                  onClick={() => selectPatient(conversation.patient)}
                  className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedPatient?.id === conversation.patient.id
                      ? "bg-accent text-accent-foreground"
                      : "hover:bg-accent/50"
                  }`}
                >
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback>
                      {conversation.patient.first_name[0]}
                      {conversation.patient.last_name[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="font-medium truncate">
                        {conversation.patient.first_name} {conversation.patient.last_name}
                      </p>
                      {conversation.unreadCount > 0 && (
                        <Badge variant="destructive" className="ml-2 px-2 py-1 text-xs">
                          {conversation.unreadCount}
                        </Badge>
                      )}
                    </div>
                    {conversation.lastMessage && (
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground truncate">
                          {conversation.lastMessage.sender_type === "doctor" ? "You: " : ""}
                          {conversation.lastMessage.message}
                        </p>
                        <span className="text-xs text-muted-foreground ml-2">
                          {new Date(conversation.lastMessage.created_at).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Chat Area */}
      <Card className="flex-1 flex flex-col">
        {selectedPatient ? (
          <>
            {/* Chat Header */}
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback>
                      {selectedPatient.first_name[0]}
                      {selectedPatient.last_name[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">
                      {selectedPatient.first_name} {selectedPatient.last_name}
                    </h3>
                    <p className="text-sm text-muted-foreground">{selectedPatient.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon">
                    <Phone className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Video className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <Separator />

            {/* Messages */}
            <CardContent className="flex-1 p-0">
              <ScrollArea className="h-full p-4">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender_type === "doctor" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-[70%] rounded-lg px-3 py-2 ${
                          message.sender_type === "doctor"
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted text-muted-foreground"
                        }`}
                      >
                        <p className="text-sm">{message.message}</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs opacity-70">
                            {new Date(message.created_at).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </span>
                          {message.sender_type === "doctor" && (
                            <span className="text-xs opacity-70 ml-2">{message.is_read ? "Read" : "Sent"}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>
            </CardContent>

            <Separator />

            {/* Message Input */}
            <CardContent className="p-4">
              <form onSubmit={handleSendMessage} className="flex items-center gap-2">
                <Button type="button" variant="outline" size="icon">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Input
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  className="flex-1"
                  disabled={isSending}
                />
                <Button type="submit" size="icon" disabled={isSending || !newMessage.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </form>
            </CardContent>
          </>
        ) : (
          <CardContent className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
              <p className="text-muted-foreground">Choose a patient from the sidebar to start messaging</p>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
