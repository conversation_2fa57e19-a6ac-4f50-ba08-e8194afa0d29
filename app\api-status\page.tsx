import { APIStatusIndicator } from "@/components/api-status-indicator"

export default function APIStatusPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">API Status</h1>
        <p className="text-muted-foreground">Monitor and configure external API integrations</p>
      </div>

      <APIStatusIndicator />

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Setup Instructions</h2>

          <div className="space-y-3">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">✅ VAPI Voice Assistant</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Pre-configured and ready to use! The voice assistant uses a demo API key that works out of the box.
              </p>
              <p className="text-xs text-green-600">No setup required - just click "Start Voice Session"</p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">🔧 Agora Video Calling</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Create an account at{" "}
                <a
                  href="https://agora.io"
                  className="text-blue-600 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  agora.io
                </a>{" "}
                and get your App ID.
              </p>
              <code className="text-xs bg-muted p-2 rounded block">NEXT_PUBLIC_AGORA_APP_ID=your_app_id_here</code>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Current Features</h2>

          <div className="space-y-3">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">✅ Working Now</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Patient management</li>
                <li>• Appointment scheduling</li>
                <li>• Medical records</li>
                <li>• AI chat assistant</li>
                <li>• Voice assistant (VAPI)</li>
                <li>• Basic voice recording</li>
                <li>• Test video calls</li>
              </ul>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">🔧 Needs Setup</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Professional video calling (Agora App ID)</li>
                <li>• Video call sharing and invitations</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
