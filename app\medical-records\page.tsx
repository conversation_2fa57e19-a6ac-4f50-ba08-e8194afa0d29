"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Plus, FileText, Calendar, User, Filter, Eye } from "lucide-react"
import { createBrowserClient } from "@supabase/ssr"
import { toast } from "@/hooks/use-toast"

interface MedicalRecord {
  id: string
  patient_id: string
  appointment_id?: string
  record_type: string
  title: string
  content: string
  diagnosis?: string
  treatment_plan?: string
  prescriptions?: any
  attachments?: any
  created_at: string
  updated_at: string
  patients: {
    first_name: string
    last_name: string
  }
  appointments?: {
    appointment_date: string
    title: string
  }
}

interface Patient {
  id: string
  first_name: string
  last_name: string
}

export default function MedicalRecordsPage() {
  const [records, setRecords] = useState<MedicalRecord[]>([])
  const [patients, setPatients] = useState<Patient[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedPatient, setSelectedPatient] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  )

  const [newRecord, setNewRecord] = useState({
    patient_id: "",
    record_type: "consultation",
    title: "",
    content: "",
    diagnosis: "",
    treatment_plan: "",
    prescriptions: "",
  })

  useEffect(() => {
    fetchRecords()
    fetchPatients()
  }, [])

  const fetchRecords = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (!user) return

      const { data, error } = await supabase
        .from("medical_records")
        .select(`
          *,
          patients (first_name, last_name),
          appointments (appointment_date, title)
        `)
        .eq("doctor_id", user.id)
        .order("created_at", { ascending: false })

      if (error) throw error
      setRecords(data || [])
    } catch (error) {
      console.error("Error fetching records:", error)
      toast({
        title: "Error",
        description: "Failed to fetch medical records",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchPatients = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (!user) return

      const { data, error } = await supabase
        .from("patients")
        .select("id, first_name, last_name")
        .eq("doctor_id", user.id)
        .order("first_name")

      if (error) throw error
      setPatients(data || [])
    } catch (error) {
      console.error("Error fetching patients:", error)
    }
  }

  const handleAddRecord = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (!user) return

      const recordData = {
        ...newRecord,
        doctor_id: user.id,
        prescriptions: newRecord.prescriptions ? JSON.parse(newRecord.prescriptions) : null,
      }

      const { error } = await supabase.from("medical_records").insert([recordData])

      if (error) throw error

      toast({
        title: "Success",
        description: "Medical record added successfully",
      })

      setIsAddDialogOpen(false)
      setNewRecord({
        patient_id: "",
        record_type: "consultation",
        title: "",
        content: "",
        diagnosis: "",
        treatment_plan: "",
        prescriptions: "",
      })
      fetchRecords()
    } catch (error) {
      console.error("Error adding record:", error)
      toast({
        title: "Error",
        description: "Failed to add medical record",
        variant: "destructive",
      })
    }
  }

  const filteredRecords = records.filter((record) => {
    const matchesSearch =
      record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${record.patients.first_name} ${record.patients.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = selectedType === "all" || record.record_type === selectedType
    const matchesPatient = selectedPatient === "all" || record.patient_id === selectedPatient

    return matchesSearch && matchesType && matchesPatient
  })

  const getRecordTypeColor = (type: string) => {
    const colors = {
      consultation: "bg-blue-100 text-blue-800",
      diagnosis: "bg-red-100 text-red-800",
      prescription: "bg-green-100 text-green-800",
      "lab-result": "bg-purple-100 text-purple-800",
      imaging: "bg-orange-100 text-orange-800",
      procedure: "bg-yellow-100 text-yellow-800",
    }
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Medical Records</h1>
          <p className="text-muted-foreground">Manage patient medical records and documentation</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Record
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Medical Record</DialogTitle>
              <DialogDescription>Create a new medical record for a patient</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="patient">Patient</Label>
                  <Select
                    value={newRecord.patient_id}
                    onValueChange={(value) => setNewRecord({ ...newRecord, patient_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select patient" />
                    </SelectTrigger>
                    <SelectContent>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.first_name} {patient.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="type">Record Type</Label>
                  <Select
                    value={newRecord.record_type}
                    onValueChange={(value) => setNewRecord({ ...newRecord, record_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="consultation">Consultation</SelectItem>
                      <SelectItem value="diagnosis">Diagnosis</SelectItem>
                      <SelectItem value="prescription">Prescription</SelectItem>
                      <SelectItem value="lab-result">Lab Result</SelectItem>
                      <SelectItem value="imaging">Imaging</SelectItem>
                      <SelectItem value="procedure">Procedure</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={newRecord.title}
                  onChange={(e) => setNewRecord({ ...newRecord, title: e.target.value })}
                  placeholder="Record title"
                />
              </div>
              <div>
                <Label htmlFor="content">Content</Label>
                <Textarea
                  id="content"
                  value={newRecord.content}
                  onChange={(e) => setNewRecord({ ...newRecord, content: e.target.value })}
                  placeholder="Detailed record content"
                  rows={4}
                />
              </div>
              <div>
                <Label htmlFor="diagnosis">Diagnosis</Label>
                <Textarea
                  id="diagnosis"
                  value={newRecord.diagnosis}
                  onChange={(e) => setNewRecord({ ...newRecord, diagnosis: e.target.value })}
                  placeholder="Diagnosis details"
                  rows={2}
                />
              </div>
              <div>
                <Label htmlFor="treatment">Treatment Plan</Label>
                <Textarea
                  id="treatment"
                  value={newRecord.treatment_plan}
                  onChange={(e) => setNewRecord({ ...newRecord, treatment_plan: e.target.value })}
                  placeholder="Treatment plan details"
                  rows={2}
                />
              </div>
              <div>
                <Label htmlFor="prescriptions">Prescriptions (JSON format)</Label>
                <Textarea
                  id="prescriptions"
                  value={newRecord.prescriptions}
                  onChange={(e) => setNewRecord({ ...newRecord, prescriptions: e.target.value })}
                  placeholder='[{"medication": "Aspirin", "dosage": "100mg", "frequency": "daily"}]'
                  rows={2}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddRecord}>Add Record</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search records, patients, or content..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="consultation">Consultation</SelectItem>
            <SelectItem value="diagnosis">Diagnosis</SelectItem>
            <SelectItem value="prescription">Prescription</SelectItem>
            <SelectItem value="lab-result">Lab Result</SelectItem>
            <SelectItem value="imaging">Imaging</SelectItem>
            <SelectItem value="procedure">Procedure</SelectItem>
          </SelectContent>
        </Select>
        <Select value={selectedPatient} onValueChange={setSelectedPatient}>
          <SelectTrigger className="w-[180px]">
            <User className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Patients</SelectItem>
            {patients.map((patient) => (
              <SelectItem key={patient.id} value={patient.id}>
                {patient.first_name} {patient.last_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Records Grid */}
      <div className="grid gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{record.title}</CardTitle>
                  <CardDescription>
                    Patient: {record.patients.first_name} {record.patients.last_name}
                    {record.appointments && (
                      <span className="ml-2">
                        • Appointment: {new Date(record.appointments.appointment_date).toLocaleDateString()}
                      </span>
                    )}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getRecordTypeColor(record.record_type)}>
                    {record.record_type.replace("-", " ")}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedRecord(record)
                      setIsViewDialogOpen(true)
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{record.content}</p>
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <span>Created: {new Date(record.created_at).toLocaleDateString()}</span>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-3 w-3" />
                  <span>{new Date(record.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredRecords.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No medical records found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedType !== "all" || selectedPatient !== "all"
              ? "Try adjusting your search or filters"
              : "Start by adding your first medical record"}
          </p>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add First Record
          </Button>
        </div>
      )}

      {/* View Record Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          {selectedRecord && (
            <>
              <DialogHeader>
                <DialogTitle>{selectedRecord.title}</DialogTitle>
                <DialogDescription>
                  Patient: {selectedRecord.patients.first_name} {selectedRecord.patients.last_name} • Type:{" "}
                  {selectedRecord.record_type.replace("-", " ")} • Date:{" "}
                  {new Date(selectedRecord.created_at).toLocaleDateString()}
                </DialogDescription>
              </DialogHeader>
              <Tabs defaultValue="content" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="diagnosis">Diagnosis</TabsTrigger>
                  <TabsTrigger value="treatment">Treatment</TabsTrigger>
                  <TabsTrigger value="prescriptions">Prescriptions</TabsTrigger>
                </TabsList>
                <TabsContent value="content" className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Record Content</Label>
                    <div className="mt-1 p-3 bg-muted rounded-md">
                      <p className="whitespace-pre-wrap">{selectedRecord.content}</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="diagnosis" className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Diagnosis</Label>
                    <div className="mt-1 p-3 bg-muted rounded-md">
                      <p className="whitespace-pre-wrap">{selectedRecord.diagnosis || "No diagnosis recorded"}</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="treatment" className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Treatment Plan</Label>
                    <div className="mt-1 p-3 bg-muted rounded-md">
                      <p className="whitespace-pre-wrap">
                        {selectedRecord.treatment_plan || "No treatment plan recorded"}
                      </p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="prescriptions" className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Prescriptions</Label>
                    <div className="mt-1 p-3 bg-muted rounded-md">
                      {selectedRecord.prescriptions ? (
                        <pre className="whitespace-pre-wrap text-sm">
                          {JSON.stringify(selectedRecord.prescriptions, null, 2)}
                        </pre>
                      ) : (
                        <p>No prescriptions recorded</p>
                      )}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
