"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Bot, Send, User, Stethoscope, Lightbulb, AlertTriangle, Trash2 } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import VapiVoiceAssistant from "@/components/vapi-voice-assistant"

interface Patient {
  id: string
  first_name: string
  last_name: string
}

interface AIConversation {
  id: string
  conversation_title: string | null
  context_type: "general" | "diagnosis" | "treatment" | "research"
  messages: any[]
  created_at: string
  updated_at: string
}

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: string
}

export default function AIAssistantPage() {
  const [patients, setPatients] = useState<Patient[]>([])
  const [conversations, setConversations] = useState<AIConversation[]>([])
  const [selectedConversation, setSelectedConversation] = useState<AIConversation | null>(null)
  const [contextType, setContextType] = useState<"general" | "diagnosis" | "treatment" | "research">("general")
  const [selectedPatient, setSelectedPatient] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [messages, setMessages] = useState<Message[]>([])
  const [isThinking, setIsThinking] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const supabase = createClient()

  // Fetch patients and conversations
  const fetchData = async () => {
    try {
      // Fetch patients
      const { data: patientsData, error: patientsError } = await supabase
        .from("patients")
        .select("id, first_name, last_name")
        .order("first_name")

      if (patientsError) throw patientsError
      setPatients(patientsData || [])

      // Fetch AI conversations
      const { data: conversationsData, error: conversationsError } = await supabase
        .from("ai_conversations")
        .select("*")
        .order("updated_at", { ascending: false })

      if (conversationsError) throw conversationsError
      setConversations(conversationsData || [])
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Error",
        description: "Failed to load data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Save conversation
  const saveConversation = async (title: string) => {
    if (messages.length === 0) return

    try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) throw new Error("Not authenticated")

      const { error } = await supabase.from("ai_conversations").insert({
        doctor_id: userData.user.id,
        patient_id: selectedPatient || null,
        conversation_title: title,
        context_type: contextType,
        messages: messages,
      })

      if (error) throw error

      toast({
        title: "Success",
        description: "Conversation saved successfully",
      })

      fetchData()
    } catch (error) {
      console.error("Error saving conversation:", error)
      toast({
        title: "Error",
        description: "Failed to save conversation",
        variant: "destructive",
      })
    }
  }

  // Load conversation
  const loadConversation = (conversation: AIConversation) => {
    setSelectedConversation(conversation)
    setContextType(conversation.context_type)
    setMessages(conversation.messages || [])
  }

  // Delete conversation
  const deleteConversation = async (conversationId: string) => {
    if (!confirm("Are you sure you want to delete this conversation?")) return

    try {
      const { error } = await supabase.from("ai_conversations").delete().eq("id", conversationId)

      if (error) throw error

      toast({
        title: "Success",
        description: "Conversation deleted successfully",
      })

      fetchData()
      if (selectedConversation?.id === conversationId) {
        setSelectedConversation(null)
        setMessages([])
      }
    } catch (error) {
      console.error("Error deleting conversation:", error)
      toast({
        title: "Error",
        description: "Failed to delete conversation",
        variant: "destructive",
      })
    }
  }

  // Send message to AI
  const sendMessage = async (messageText: string) => {
    if (!messageText.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: messageText,
      timestamp: new Date().toISOString(),
    }

    setMessages((prev) => [...prev, userMessage])
    setIsThinking(true)

    try {
      const response = await fetch("/api/ai-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [userMessage],
        }),
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.content || "I apologize, but I encountered an issue processing your request.",
        timestamp: new Date().toISOString(),
      }

      setMessages((prev) => [...prev, assistantMessage])
    } catch (error) {
      console.error("Error sending message:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "I apologize, but I encountered a technical issue. Please try again later.",
        timestamp: new Date().toISOString(),
      }
      setMessages((prev) => [...prev, errorMessage])

      toast({
        title: "Error",
        description: "Failed to get AI response",
        variant: "destructive",
      })
    } finally {
      setIsThinking(false)
    }
  }

  // Handle message send
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    const form = e.target as HTMLFormElement
    const input = form.message as HTMLInputElement
    const message = input.value.trim()

    if (!message) return

    let contextualMessage = message

    // Add context if patient is selected
    if (selectedPatient) {
      const patient = patients.find((p) => p.id === selectedPatient)
      if (patient) {
        contextualMessage = `[Patient Context: ${patient.first_name} ${patient.last_name} (ID: ${selectedPatient})] ${message}`
      }
    }

    // Add context type
    contextualMessage = `[Context: ${contextType}] ${contextualMessage}`

    sendMessage(contextualMessage)
    input.value = ""
  }

  // Scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Quick prompts for medical assistance
  const quickPrompts = [
    {
      category: "Patient Care",
      prompts: [
        "Review recent lab results and suggest follow-up actions",
        "Analyze symptoms and provide differential diagnosis suggestions",
        "Check for drug interactions with current medications",
        "Suggest treatment options for the current condition",
      ],
    },
    {
      category: "Clinical Decision Support",
      prompts: [
        "What are the latest guidelines for hypertension management?",
        "Recommend screening protocols for diabetes",
        "Provide information about contraindications for this medication",
        "Suggest when to refer to a specialist",
      ],
    },
    {
      category: "Administrative",
      prompts: [
        "Review today's appointment schedule",
        "Identify patients due for follow-up appointments",
        "Check for incomplete medical records",
        "Summarize recent patient interactions",
      ],
    },
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading AI Assistant...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex gap-6">
      {/* Sidebar with conversations and context */}
      <Card className="w-80 flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Assistant
          </CardTitle>
          <CardDescription>Medical AI consultation and support</CardDescription>
        </CardHeader>
        <CardContent className="flex-1 space-y-4">
          {/* Context Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Context Type</label>
            <Select value={contextType} onValueChange={(value: any) => setContextType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Consultation</SelectItem>
                <SelectItem value="diagnosis">Diagnosis Support</SelectItem>
                <SelectItem value="treatment">Treatment Planning</SelectItem>
                <SelectItem value="research">Medical Research</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Patient Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Patient Context (Optional)</label>
            <Select value={selectedPatient} onValueChange={setSelectedPatient}>
              <SelectTrigger>
                <SelectValue placeholder="Select patient" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No specific patient</SelectItem>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id}>
                    {patient.first_name} {patient.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Saved Conversations */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recent Conversations</h4>
            <ScrollArea className="h-32">
              <div className="space-y-1">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors ${
                      selectedConversation?.id === conversation.id
                        ? "bg-accent text-accent-foreground"
                        : "hover:bg-accent/50"
                    }`}
                  >
                    <div className="flex-1 min-w-0" onClick={() => loadConversation(conversation)}>
                      <p className="text-sm font-medium truncate">
                        {conversation.conversation_title || "Untitled Conversation"}
                      </p>
                      <div className="flex items-center gap-1">
                        <Badge variant="outline" className="text-xs">
                          {conversation.context_type}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(conversation.updated_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation()
                        deleteConversation(conversation.id)
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          <Separator />

          {/* Save Current Conversation */}
          {messages.length > 0 && (
            <Button
              variant="outline"
              className="w-full bg-transparent"
              onClick={() => {
                const title = prompt("Enter a title for this conversation:")
                if (title) saveConversation(title)
              }}
            >
              Save Conversation
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col gap-6">
        <VapiVoiceAssistant
          medicalContext={contextType}
          patientId={selectedPatient !== "none" ? selectedPatient : undefined}
          onTranscriptUpdate={(transcript) => {
            // Auto-send voice transcript as message
            if (transcript.trim()) {
              sendMessage(transcript)
            }
          }}
        />

        {/* Quick Prompts */}
        {messages.length === 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Quick Medical Prompts
              </CardTitle>
              <CardDescription>Click on any prompt to get started with AI assistance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                {quickPrompts.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <h4 className="font-medium text-sm">{category.category}</h4>
                    <div className="space-y-1">
                      {category.prompts.map((prompt, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="w-full text-left justify-start h-auto p-2 text-xs bg-transparent"
                          onClick={() => sendMessage(prompt)}
                        >
                          {prompt}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chat Messages */}
        <Card className="flex-1 flex flex-col">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Stethoscope className="h-5 w-5" />
                <span className="font-medium">Medical AI Assistant</span>
                <Badge variant="outline">{contextType}</Badge>
                {selectedPatient && selectedPatient !== "none" && (
                  <Badge variant="secondary">
                    {patients.find((p) => p.id === selectedPatient)?.first_name}{" "}
                    {patients.find((p) => p.id === selectedPatient)?.last_name}
                  </Badge>
                )}
              </div>
              {isThinking && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  AI is thinking...
                </div>
              )}
            </div>
          </CardHeader>

          <Separator />

          <CardContent className="flex-1 p-0">
            <ScrollArea className="h-full p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center py-8">
                    <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">AI Medical Assistant Ready</h3>
                    <p className="text-muted-foreground">
                      Ask me about patient care, diagnosis, treatment, or medical research. I can provide evidence-based
                      guidance and support your medical practice.
                    </p>
                    <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg border border-orange-200 dark:border-orange-800">
                      <div className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm font-medium">Medical Disclaimer</span>
                      </div>
                      <p className="text-xs text-orange-600 dark:text-orange-300 mt-1">
                        This AI assistant provides information and suggestions only. Always use your clinical judgment
                        and verify information with current medical literature.
                      </p>
                    </div>
                  </div>
                )}

                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-3 ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        {message.role === "user" ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                        <span className="text-sm font-medium">{message.role === "user" ? "You" : "AI Assistant"}</span>
                        <span className="text-xs opacity-70">
                          {new Date(message.timestamp).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                        </span>
                      </div>
                      <div className="prose prose-sm max-w-none">
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </CardContent>

          <Separator />

          {/* Message Input */}
          <CardContent className="p-4">
            <form onSubmit={handleSendMessage} className="flex items-center gap-2">
              <Input
                name="message"
                placeholder="Ask about patient care, diagnosis, treatment, or medical research..."
                className="flex-1"
                disabled={isThinking}
              />
              <Button type="submit" size="icon" disabled={isThinking}>
                <Send className="h-4 w-4" />
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
