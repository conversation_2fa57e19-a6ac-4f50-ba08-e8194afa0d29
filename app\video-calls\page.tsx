"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import { Video, Plus, Clock, Calendar, MoreHorizontal, Play, Square, Search, Filter, VideoOff } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { AgoraVideoCall } from "@/components/agora-video-call"

interface VideoCall {
  id: string
  call_type: "scheduled" | "instant"
  status: "pending" | "active" | "ended" | "cancelled"
  room_id: string | null
  started_at: string | null
  ended_at: string | null
  duration_minutes: number | null
  recording_url: string | null
  created_at: string
  patient: {
    id: string
    first_name: string
    last_name: string
    email: string | null
    phone: string | null
  } | null
  appointment?: {
    id: string
    title: string
    appointment_date: string
  }
}

interface Patient {
  id: string
  first_name: string
  last_name: string
  email: string | null
  phone: string | null
}

export default function VideoCallsPage() {
  const [videoCalls, setVideoCalls] = useState<VideoCall[]>([])
  const [filteredCalls, setFilteredCalls] = useState<VideoCall[]>([])
  const [patients, setPatients] = useState<Patient[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [isLoading, setIsLoading] = useState(true)
  const [isStartCallDialogOpen, setIsStartCallDialogOpen] = useState(false)
  const [activeCall, setActiveCall] = useState<VideoCall | null>(null)
  const [isCallActive, setIsCallActive] = useState(false)
  const [isTestMode, setIsTestMode] = useState(false)

  // Form state for starting calls
  const [formData, setFormData] = useState({
    patient_id: "",
    call_type: "instant" as "scheduled" | "instant",
  })

  const supabase = createClient()

  // Fetch video calls
  const fetchVideoCalls = async () => {
    try {
      const { data, error } = await supabase
        .from("video_calls")
        .select(
          `
          *,
          patient:patients(id, first_name, last_name, email, phone),
          appointment:appointments(id, title, appointment_date)
        `,
        )
        .order("created_at", { ascending: false })

      if (error) throw error
      setVideoCalls(data || [])
      setFilteredCalls(data || [])
    } catch (error) {
      console.error("Error fetching video calls:", error)
      toast({
        title: "Error",
        description: "Failed to fetch video calls",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch patients
  const fetchPatients = async () => {
    try {
      const { data, error } = await supabase.from("patients").select("id, first_name, last_name, email, phone")

      if (error) throw error
      setPatients(data || [])
    } catch (error) {
      console.error("Error fetching patients:", error)
    }
  }

  useEffect(() => {
    fetchVideoCalls()
    fetchPatients()
  }, [])

  // Filter video calls
  useEffect(() => {
    let filtered = videoCalls

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (call) =>
          call.patient &&
          (`${call.patient.first_name} ${call.patient.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
            call.patient.email?.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((call) => call.status === statusFilter)
    }

    // Type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter((call) => call.call_type === typeFilter)
    }

    setFilteredCalls(filtered)
  }, [videoCalls, searchTerm, statusFilter, typeFilter])

  // Start video call
  const handleStartCall = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) throw new Error("Not authenticated")

      const roomId = `medical_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const callData: any = {
        doctor_id: userData.user.id,
        call_type: formData.call_type,
        status: "pending",
        room_id: roomId,
      }

      // Only add patient_id if not in test mode and patient is selected
      if (!isTestMode && formData.patient_id) {
        callData.patient_id = formData.patient_id
      }

      const { data, error } = await supabase
        .from("video_calls")
        .insert(callData)
        .select(
          `
          *,
          patient:patients(id, first_name, last_name, email, phone)
        `,
        )
        .single()

      if (error) throw error

      toast({
        title: "Success",
        description: isTestMode ? "Test video call started successfully" : "Video call started successfully",
      })

      setIsStartCallDialogOpen(false)
      resetForm()
      fetchVideoCalls()

      // Set as active call
      setActiveCall(data)
      setIsCallActive(true)
    } catch (error) {
      console.error("Error starting video call:", error)
      toast({
        title: "Error",
        description: "Failed to start video call",
        variant: "destructive",
      })
    }
  }

  // Join existing call
  const handleJoinCall = async (call: VideoCall) => {
    try {
      const { error } = await supabase
        .from("video_calls")
        .update({
          status: "active",
          started_at: new Date().toISOString(),
        })
        .eq("id", call.id)

      if (error) throw error

      setActiveCall(call)
      setIsCallActive(true)
      fetchVideoCalls()

      toast({
        title: "Joined Call",
        description: `Joined video call with ${call.patient ? `${call.patient.first_name} ${call.patient.last_name}` : "Test Call"}`,
      })
    } catch (error) {
      console.error("Error joining call:", error)
      toast({
        title: "Error",
        description: "Failed to join video call",
        variant: "destructive",
      })
    }
  }

  // End call
  const handleEndCall = async () => {
    if (!activeCall) return

    try {
      const startTime = new Date(activeCall.started_at || activeCall.created_at)
      const endTime = new Date()
      const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60))

      const { error } = await supabase
        .from("video_calls")
        .update({
          status: "ended",
          ended_at: endTime.toISOString(),
          duration_minutes: durationMinutes,
        })
        .eq("id", activeCall.id)

      if (error) throw error

      setActiveCall(null)
      setIsCallActive(false)
      fetchVideoCalls()

      toast({
        title: "Call Ended",
        description: `Call duration: ${durationMinutes} minutes`,
      })
    } catch (error) {
      console.error("Error ending call:", error)
      toast({
        title: "Error",
        description: "Failed to end video call",
        variant: "destructive",
      })
    }
  }

  // Cancel call
  const handleCancelCall = async (callId: string) => {
    try {
      const { error } = await supabase.from("video_calls").update({ status: "cancelled" }).eq("id", callId)

      if (error) throw error

      toast({
        title: "Success",
        description: "Video call cancelled",
      })

      fetchVideoCalls()
    } catch (error) {
      console.error("Error cancelling call:", error)
      toast({
        title: "Error",
        description: "Failed to cancel video call",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      patient_id: "",
      call_type: "instant",
    })
    setIsTestMode(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "ended":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
      case "cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading video calls...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Video Calls</h1>
          <p className="text-muted-foreground">Manage your video consultations with Agora integration</p>
        </div>
        <div className="flex items-center gap-3">
          {isCallActive && activeCall && (
            <div className="flex items-center gap-2 px-3 py-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Call with{" "}
                {activeCall.patient ? `${activeCall.patient.first_name} ${activeCall.patient.last_name}` : "Test Call"}
              </span>
              <Button size="sm" variant="destructive" onClick={handleEndCall}>
                <Square className="h-4 w-4 mr-1" />
                End Call
              </Button>
            </div>
          )}
          <Dialog open={isStartCallDialogOpen} onOpenChange={setIsStartCallDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Start Video Call
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Start Video Call</DialogTitle>
                <DialogDescription>Start a video consultation with Agora or test the functionality.</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleStartCall} className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="testMode"
                    checked={isTestMode}
                    onChange={(e) => setIsTestMode(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="testMode">Test Mode (No patient required)</Label>
                </div>

                {!isTestMode && (
                  <div className="space-y-2">
                    <Label htmlFor="patient_id">Patient *</Label>
                    <Select
                      value={formData.patient_id}
                      onValueChange={(value) => setFormData({ ...formData, patient_id: value })}
                      required={!isTestMode}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a patient" />
                      </SelectTrigger>
                      <SelectContent>
                        {patients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id}>
                            {patient.first_name} {patient.last_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="call_type">Call Type</Label>
                  <Select
                    value={formData.call_type}
                    onValueChange={(value: "scheduled" | "instant") => setFormData({ ...formData, call_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="instant">{isTestMode ? "Test Call" : "Instant Call"}</SelectItem>
                      <SelectItem value="scheduled">Scheduled Call</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-3">
                  <Button type="button" variant="outline" onClick={() => setIsStartCallDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">{isTestMode ? "Start Test Call" : "Start Call"}</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search video calls by patient name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="ended">Ended</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="instant">Instant</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Call Interface with Agora */}
      {isCallActive && activeCall && (
        <AgoraVideoCall
          channelName={activeCall.room_id || `call_${activeCall.id}`}
          onCallEnd={handleEndCall}
          patientName={
            activeCall.patient ? `${activeCall.patient.first_name} ${activeCall.patient.last_name}` : "Test Call"
          }
        />
      )}

      {/* Video Calls Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredCalls.map((call) => (
          <Card key={call.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback>
                      {call.patient ? `${call.patient.first_name[0]}${call.patient.last_name[0]}` : "TC"}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">
                      {call.patient ? `${call.patient.first_name} ${call.patient.last_name}` : "Test Call"}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge className={getStatusColor(call.status)}>{call.status}</Badge>
                      <Badge variant="outline">{call.call_type}</Badge>
                    </CardDescription>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {call.status === "pending" && (
                      <DropdownMenuItem onClick={() => handleJoinCall(call)}>
                        <Play className="h-4 w-4 mr-2" />
                        Join Call
                      </DropdownMenuItem>
                    )}
                    {(call.status === "pending" || call.status === "active") && (
                      <DropdownMenuItem onClick={() => handleCancelCall(call.id)} className="text-destructive">
                        <VideoOff className="h-4 w-4 mr-2" />
                        Cancel Call
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                {new Date(call.created_at).toLocaleDateString()}
              </div>
              {call.started_at && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  Started: {new Date(call.started_at).toLocaleTimeString()}
                </div>
              )}
              {call.duration_minutes && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  Duration: {call.duration_minutes} minutes
                </div>
              )}
              {call.room_id && <div className="text-xs text-muted-foreground">Room: {call.room_id}</div>}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCalls.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Video className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No video calls found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm || statusFilter !== "all" || typeFilter !== "all"
                ? "Try adjusting your search or filters"
                : "Get started by scheduling your first video call with Agora"}
            </p>
            {!searchTerm && statusFilter === "all" && typeFilter === "all" && patients.length > 0 && (
              <Button onClick={() => setIsStartCallDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Start Video Call
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
