"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AlertCircle, CheckCircle, XCircle, RefreshCw } from "lucide-react"
import { checkAPIStatus, validateClientEnvironment } from "@/lib/config"
import { toast } from "@/hooks/use-toast"

interface APIStatus {
  gemini: boolean
  agora: boolean
}

export function APIStatusIndicator() {
  const [status, setStatus] = useState<APIStatus>({ gemini: false, agora: false })
  const [isLoading, setIsLoading] = useState(true)
  const [missingEnvVars, setMissingEnvVars] = useState<string[]>([])

  const checkStatus = async () => {
    setIsLoading(true)
    try {
      const apiStatus = await checkAPIStatus()
      setStatus(apiStatus)
      setMissingEnvVars(validateClientEnvironment())
    } catch (error) {
      console.error("Failed to check API status:", error)
      toast({
        title: "Status Check Failed",
        description: "Unable to verify API connectivity",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkStatus()
  }, [])

  const getStatusIcon = (isWorking: boolean) => {
    if (isLoading) return <RefreshCw className="h-4 w-4 animate-spin" />
    return isWorking ? <CheckCircle className="h-4 w-4 text-green-600" /> : <XCircle className="h-4 w-4 text-red-600" />
  }

  const getStatusBadge = (isWorking: boolean) => {
    if (isLoading) return <Badge variant="secondary">Checking...</Badge>
    return <Badge variant={isWorking ? "default" : "destructive"}>{isWorking ? "Connected" : "Disconnected"}</Badge>
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              API Status Dashboard
            </CardTitle>
            <CardDescription>Monitor the status of external API integrations</CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={checkStatus} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {missingEnvVars.length > 0 && (
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Missing Environment Variables</span>
            </div>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              Missing: {missingEnvVars.join(", ")}. Please add them to your Vercel project settings for full
              functionality.
            </p>
          </div>
        )}

        <div className="grid gap-4 md:grid-cols-3">
          {/* Gemini AI Assistant */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              {getStatusIcon(status.gemini)}
              <div>
                <p className="font-medium">Gemini AI</p>
                <p className="text-sm text-muted-foreground">Chat Assistant</p>
              </div>
            </div>
            {getStatusBadge(status.gemini)}
          </div>

          {/* VAPI Voice Assistant */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="font-medium">VAPI</p>
                <p className="text-sm text-muted-foreground">Voice Assistant</p>
              </div>
            </div>
            <Badge variant="default">Ready</Badge>
          </div>

          {/* Agora Video Calls */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              {getStatusIcon(status.agora)}
              <div>
                <p className="font-medium">Agora</p>
                <p className="text-sm text-muted-foreground">Video Calls</p>
              </div>
            </div>
            {getStatusBadge(status.agora)}
          </div>
        </div>

        {/* Environment Variables Guide */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Required Environment Variables:</h4>
          <div className="text-xs text-muted-foreground space-y-1 font-mono bg-muted p-3 rounded-lg">
            <div>NEXT_PUBLIC_AGORA_APP_ID=your_agora_app_id</div>
            <div className="text-green-600">✓ VAPI voice assistant is pre-configured</div>
          </div>
          <p className="text-xs text-muted-foreground">
            Add the Agora App ID to your Vercel project settings under Environment Variables.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
