"use client"

import { useEffect, useRef, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>, PhoneOff } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface VAPIVoiceAgentProps {
  onCallEnd: (transcript: string, summary: string) => void
  patientName?: string
  sessionType: "dictation" | "consultation" | "notes"
}

// VAPI configuration
const VAPI_CONFIG = {
  apiKey: "your-vapi-api-key", // Your VAPI API key
  assistantId: "your-assistant-id", // Your VAPI assistant ID
}

export function VAPIVoiceAgent({ onCallEnd, patientName, sessionType }: VAPIVoiceAgentProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isCallActive, setIsCallActive] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [transcript, setTranscript] = useState("")
  const [callDuration, setCallDuration] = useState(0)
  const [vapiClient, setVapiClient] = useState<any>(null)

  const callStartTime = useRef<Date | null>(null)
  const durationInterval = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Initialize VAPI client
    const initializeVAPI = async () => {
      try {
        // Dynamically import VAPI SDK
        const VAPI = (await import("@vapi-ai/web")).default

        const client = new VAPI(VAPI_CONFIG.apiKey)
        setVapiClient(client)

        // Set up event listeners
        client.on("call-start", () => {
          console.log("[v0] VAPI call started")
          setIsCallActive(true)
          setIsConnected(true)
          callStartTime.current = new Date()

          // Start duration timer
          durationInterval.current = setInterval(() => {
            if (callStartTime.current) {
              const duration = Math.floor((Date.now() - callStartTime.current.getTime()) / 1000)
              setCallDuration(duration)
            }
          }, 1000)

          toast({
            title: "Voice Call Started",
            description: `${sessionType} session is now active`,
          })
        })

        client.on("call-end", () => {
          console.log("[v0] VAPI call ended")
          setIsCallActive(false)
          setIsConnected(false)

          if (durationInterval.current) {
            clearInterval(durationInterval.current)
          }

          toast({
            title: "Voice Call Ended",
            description: `Session duration: ${formatTime(callDuration)}`,
          })
        })

        client.on("speech-start", () => {
          console.log("[v0] Speech started")
        })

        client.on("speech-end", () => {
          console.log("[v0] Speech ended")
        })

        client.on("message", (message: any) => {
          console.log("[v0] VAPI message:", message)
          if (message.type === "transcript" && message.transcript) {
            setTranscript((prev) => prev + " " + message.transcript)
          }
        })

        client.on("error", (error: any) => {
          console.error("[v0] VAPI error:", error)
          toast({
            title: "Voice Call Error",
            description: "There was an issue with the voice call",
            variant: "destructive",
          })
        })

        console.log("[v0] VAPI client initialized successfully")
      } catch (error) {
        console.error("[v0] Error initializing VAPI:", error)
        toast({
          title: "Initialization Error",
          description: "Failed to initialize voice agent",
          variant: "destructive",
        })
      }
    }

    initializeVAPI()

    // Cleanup on unmount
    return () => {
      if (vapiClient && isCallActive) {
        vapiClient.stop()
      }
      if (durationInterval.current) {
        clearInterval(durationInterval.current)
      }
    }
  }, [])

  const startCall = async () => {
    if (!vapiClient) {
      toast({
        title: "Error",
        description: "Voice agent not initialized",
        variant: "destructive",
      })
      return
    }

    try {
      // Create assistant configuration based on session type
      const assistantConfig = {
        model: {
          provider: "openai",
          model: "gpt-4",
          messages: [
            {
              role: "system",
              content: getSystemPrompt(sessionType, patientName),
            },
          ],
        },
        voice: {
          provider: "11labs",
          voiceId: "21m00Tcm4TlvDq8ikWAM", // Professional voice
        },
        firstMessage: getFirstMessage(sessionType, patientName),
      }

      await vapiClient.start(assistantConfig)
    } catch (error) {
      console.error("[v0] Error starting VAPI call:", error)
      toast({
        title: "Call Start Error",
        description: "Failed to start voice call",
        variant: "destructive",
      })
    }
  }

  const endCall = async () => {
    if (!vapiClient) return

    try {
      await vapiClient.stop()

      // Generate summary (in production, this would be done by AI)
      const summary = generateSummary(transcript, sessionType)

      onCallEnd(transcript, summary)
    } catch (error) {
      console.error("[v0] Error ending VAPI call:", error)
      toast({
        title: "Call End Error",
        description: "Failed to end voice call properly",
        variant: "destructive",
      })
    }
  }

  const toggleMute = () => {
    if (vapiClient && isCallActive) {
      vapiClient.setMuted(!isMuted)
      setIsMuted(!isMuted)
    }
  }

  const getSystemPrompt = (type: string, patient?: string) => {
    const basePrompt = "You are a medical AI assistant helping with "
    const patientContext = patient ? ` for patient ${patient}` : ""

    switch (type) {
      case "dictation":
        return `${basePrompt}medical dictation${patientContext}. Listen carefully and help organize medical notes, diagnoses, and treatment plans. Ask clarifying questions when needed.`
      case "consultation":
        return `${basePrompt}a patient consultation${patientContext}. Assist with medical decision-making, provide evidence-based suggestions, and help document the consultation.`
      case "notes":
        return `${basePrompt}clinical note-taking${patientContext}. Help organize and structure clinical observations, assessments, and plans.`
      default:
        return `${basePrompt}general medical assistance${patientContext}. Provide professional medical support and documentation assistance.`
    }
  }

  const getFirstMessage = (type: string, patient?: string) => {
    const patientGreeting = patient ? ` for ${patient}` : ""

    switch (type) {
      case "dictation":
        return `Hello! I'm ready to assist with your medical dictation${patientGreeting}. Please begin when you're ready.`
      case "consultation":
        return `Hello! I'm here to assist with your patient consultation${patientGreeting}. How can I help you today?`
      case "notes":
        return `Hello! I'm ready to help you with clinical notes${patientGreeting}. Please share what you'd like to document.`
      default:
        return `Hello! I'm your medical AI assistant${patientGreeting}. How can I help you today?`
    }
  }

  const generateSummary = (transcript: string, type: string) => {
    // In production, this would use AI to generate a proper summary
    if (!transcript.trim()) return "No transcript available"

    const words = transcript.split(" ").length
    return `${type.charAt(0).toUpperCase() + type.slice(1)} session completed. Transcript contains ${words} words. Key points and medical information documented.`
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>VAPI Voice Agent</span>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`} />
            <span className="text-sm text-muted-foreground">{isConnected ? "Connected" : "Disconnected"}</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Session Info */}
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div>
            <p className="font-medium capitalize">{sessionType} Session</p>
            {patientName && <p className="text-sm text-muted-foreground">Patient: {patientName}</p>}
          </div>
          <div className="text-right">
            <p className="font-mono text-lg">{formatTime(callDuration)}</p>
            <Badge variant={isCallActive ? "default" : "secondary"}>{isCallActive ? "Active" : "Inactive"}</Badge>
          </div>
        </div>

        {/* Call Controls */}
        <div className="flex justify-center gap-4">
          {!isCallActive ? (
            <Button size="lg" onClick={startCall} className="px-8" disabled={!vapiClient}>
              <Phone className="h-5 w-5 mr-2" />
              Start Voice Call
            </Button>
          ) : (
            <>
              <Button
                variant={isMuted ? "destructive" : "default"}
                size="icon"
                onClick={toggleMute}
                className="rounded-full"
              >
                {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>

              <Button variant="destructive" size="icon" onClick={endCall} className="rounded-full">
                <PhoneOff className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>

        {/* Live Transcript */}
        {isCallActive && (
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              Live Transcript
            </h4>
            <div className="max-h-32 overflow-y-auto">
              <p className="text-sm whitespace-pre-wrap">
                {transcript || "Listening... Start speaking to see transcript."}
              </p>
            </div>
          </div>
        )}

        {/* Instructions */}
        {!isCallActive && (
          <div className="text-center text-sm text-muted-foreground">
            <p>Click "Start Voice Call" to begin your {sessionType} session with VAPI AI.</p>
            <p className="mt-1">The AI will assist you with medical documentation and note-taking.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
