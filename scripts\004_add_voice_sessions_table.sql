-- Create voice sessions table for VAPI integration
CREATE TABLE IF NOT EXISTS public.voice_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  doctor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  patient_id UUID REFERENCES public.patients(id) ON DELETE SET NULL,
  session_type TEXT DEFAULT 'consultation' CHECK (session_type IN ('consultation', 'dictation', 'notes')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  transcript TEXT,
  audio_url TEXT,
  summary TEXT,
  duration_seconds INTEGER,
  vapi_call_id TEXT,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.voice_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for voice sessions
CREATE POLICY "voice_sessions_select_own" ON public.voice_sessions FOR SELECT USING (auth.uid() = doctor_id);
CREATE POLICY "voice_sessions_insert_own" ON public.voice_sessions FOR INSERT WITH CHECK (auth.uid() = doctor_id);
CREATE POLICY "voice_sessions_update_own" ON public.voice_sessions FOR UPDATE USING (auth.uid() = doctor_id);
CREATE POLICY "voice_sessions_delete_own" ON public.voice_sessions FOR DELETE USING (auth.uid() = doctor_id);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_voice_sessions_doctor_id ON public.voice_sessions(doctor_id);
CREATE INDEX IF NOT EXISTS idx_voice_sessions_patient_id ON public.voice_sessions(patient_id);
