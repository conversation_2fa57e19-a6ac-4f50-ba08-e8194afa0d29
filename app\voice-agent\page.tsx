"use client"
import { useState, useEffect, useRef } from "react"
import { createClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Mic,
  Play,
  Pause,
  Square,
  Volume2,
  VolumeX,
  Settings,
  FileText,
  Clock,
  User,
  Bot,
  Download,
  Trash2,
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import VapiVoiceAssistant from "@/components/vapi-voice-assistant"

interface VoiceSession {
  id: string
  session_type: "dictation" | "consultation" | "notes"
  status: "active" | "completed" | "paused"
  duration_seconds: number
  transcript: string | null
  summary: string | null
  audio_url: string | null
  created_at: string
  updated_at: string
  patient?: {
    id: string
    first_name: string
    last_name: string
  }
}

interface Patient {
  id: string
  first_name: string
  last_name: string
}

export default function VoiceAgentPage() {
  const [voiceSessions, setVoiceSessions] = useState<VoiceSession[]>([])
  const [patients, setPatients] = useState<Patient[]>([])
  const [activeSession, setActiveSession] = useState<VoiceSession | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [sessionType, setSessionType] = useState<"dictation" | "consultation" | "notes">("dictation")
  const [selectedPatient, setSelectedPatient] = useState<string>("none") // Updated default value to "none"
  const [currentTranscript, setCurrentTranscript] = useState("")
  const [recordingTime, setRecordingTime] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)

  const [voiceSettings, setVoiceSettings] = useState({
    rate: 0.85, // Slightly slower for more natural speech
    pitch: 1.1, // Slightly higher pitch for friendliness
    volume: 0.9,
    voice: "natural", // Voice selection
  })

  // Voice recognition and synthesis
  const [recognitionInstance, setRecognitionInstance] = useState<any | null>(null)
  const [synthesis, setSynthesis] = useState<SpeechSynthesis | null>(null)
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])

  const supabase = createClient()

  // Initialize speech recognition and synthesis
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Speech Recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      if (SpeechRecognition) {
        const recognition = new SpeechRecognition()
        recognition.continuous = true
        recognition.interimResults = true
        recognition.lang = "en-US"
        recognition.maxAlternatives = 3

        recognition.onresult = (event) => {
          let transcript = ""
          for (let i = event.resultIndex; i < event.results.length; i++) {
            transcript += event.results[i][0].transcript
          }
          setCurrentTranscript(transcript)
        }

        recognition.onerror = (event) => {
          console.error("Speech recognition error:", event.error)
          toast({
            title: "Voice Recognition Error",
            description: "There was an issue with voice recognition",
            variant: "destructive",
          })
        }

        setRecognitionInstance(recognition)
      }

      // Speech Synthesis
      if (window.speechSynthesis) {
        setSynthesis(window.speechSynthesis)
      }
    }
  }, [])

  // Fetch data
  const fetchData = async () => {
    try {
      // Fetch patients
      const { data: patientsData, error: patientsError } = await supabase
        .from("patients")
        .select("id, first_name, last_name")
        .order("first_name")

      if (patientsError) throw patientsError
      setPatients(patientsData || [])

      // Fetch voice sessions
      const { data: sessionsData, error: sessionsError } = await supabase
        .from("voice_sessions")
        .select(`
          *,
          patient:patients(id, first_name, last_name)
        `)
        .order("created_at", { ascending: false })

      if (sessionsError) throw sessionsError
      setVoiceSessions(sessionsData || [])
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Error",
        description: "Failed to load data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Start recording
  const startRecording = async () => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      // Start media recorder
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data)
      }

      mediaRecorder.start()

      // Start speech recognition
      if (recognitionInstance) {
        recognitionInstance.start()
      }

      // Create new session
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) throw new Error("Not authenticated")

      const { data: newSession, error } = await supabase
        .from("voice_sessions")
        .insert({
          doctor_id: userData.user.id,
          patient_id: selectedPatient === "none" ? null : selectedPatient,
          session_type: sessionType,
          status: "active",
          duration_seconds: 0,
        })
        .select(`
          *,
          patient:patients(id, first_name, last_name)
        `)
        .single()

      if (error) throw error

      setActiveSession(newSession)
      setIsRecording(true)
      setRecordingTime(0)
      setCurrentTranscript("")

      // Start timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)

      toast({
        title: "Recording Started",
        description: `${sessionType} session is now active`,
      })
    } catch (error) {
      console.error("Error starting recording:", error)
      toast({
        title: "Error",
        description: "Failed to start recording",
        variant: "destructive",
      })
    }
  }

  // Stop recording
  const stopRecording = async () => {
    try {
      // Stop media recorder
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop()
      }

      // Stop speech recognition
      if (recognitionInstance) {
        recognitionInstance.stop()
      }

      // Stop timer
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current)
      }

      // Update session
      if (activeSession) {
        const { error } = await supabase
          .from("voice_sessions")
          .update({
            status: "completed",
            duration_seconds: recordingTime,
            transcript: currentTranscript,
          })
          .eq("id", activeSession.id)

        if (error) throw error
      }

      setIsRecording(false)
      setActiveSession(null)
      fetchData()

      toast({
        title: "Recording Stopped",
        description: "Session has been saved successfully",
      })
    } catch (error) {
      console.error("Error stopping recording:", error)
      toast({
        title: "Error",
        description: "Failed to stop recording",
        variant: "destructive",
      })
    }
  }

  // Pause/Resume recording
  const togglePause = () => {
    if (isPaused) {
      if (recognitionInstance) {
        recognitionInstance.start()
      }
      if (recordingIntervalRef.current) {
        recordingIntervalRef.current = setInterval(() => {
          setRecordingTime((prev) => prev + 1)
        }, 1000)
      }
    } else {
      if (recognitionInstance) {
        recognitionInstance.stop()
      }
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current)
      }
    }
    setIsPaused(!isPaused)
  }

  // Toggle mute
  const toggleMute = () => {
    setIsMuted(!isMuted)
    // In a real implementation, this would mute the microphone
  }

  const speakText = (text: string) => {
    if (synthesis && !isMuted) {
      // Cancel any ongoing speech
      synthesis.cancel()

      const utterance = new SpeechSynthesisUtterance(text)

      utterance.rate = voiceSettings.rate
      utterance.pitch = voiceSettings.pitch
      utterance.volume = voiceSettings.volume

      const voices = synthesis.getVoices()
      const preferredVoices = voices.filter(
        (voice) =>
          voice.name.includes("Natural") ||
          voice.name.includes("Neural") ||
          voice.name.includes("Premium") ||
          (voice.lang.startsWith("en") && voice.localService),
      )

      if (preferredVoices.length > 0) {
        // Prefer female voices for medical applications (often perceived as more caring)
        const femaleVoice = preferredVoices.find(
          (voice) =>
            voice.name.toLowerCase().includes("female") ||
            voice.name.toLowerCase().includes("woman") ||
            voice.name.toLowerCase().includes("sarah") ||
            voice.name.toLowerCase().includes("samantha"),
        )
        utterance.voice = femaleVoice || preferredVoices[0]
      }

      const processedText = text
        .replace(/\./g, ". ") // Add pause after periods
        .replace(/,/g, ", ") // Add pause after commas
        .replace(/:/g, ": ") // Add pause after colons
        .replace(/;/g, "; ") // Add pause after semicolons

      utterance.text = processedText

      utterance.onstart = () => {
        console.log("[v0] Speech synthesis started")
      }

      utterance.onend = () => {
        console.log("[v0] Speech synthesis ended")
      }

      utterance.onerror = (event) => {
        console.error("[v0] Speech synthesis error:", event.error)
      }

      synthesis.speak(utterance)
    }
  }

  // Delete session
  const deleteSession = async (sessionId: string) => {
    if (!confirm("Are you sure you want to delete this voice session?")) return

    try {
      const { error } = await supabase.from("voice_sessions").delete().eq("id", sessionId)

      if (error) throw error

      toast({
        title: "Success",
        description: "Voice session deleted successfully",
      })

      fetchData()
    } catch (error) {
      console.error("Error deleting session:", error)
      toast({
        title: "Error",
        description: "Failed to delete voice session",
        variant: "destructive",
      })
    }
  }

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  useEffect(() => {
    fetchData()
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading Voice Agent...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Voice Agent</h1>
          <p className="text-muted-foreground">AI-powered voice dictation and consultation with VAPI integration</p>
        </div>
        <div className="flex items-center gap-3">
          {isRecording && (
            <div className="flex items-center gap-2 px-3 py-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-red-800 dark:text-red-200">
                Recording: {formatTime(recordingTime)}
              </span>
            </div>
          )}
          <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <Settings className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Voice Agent Settings</DialogTitle>
                <DialogDescription>Configure your voice recording and synthesis preferences</DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Session Type</label>
                  <Select value={sessionType} onValueChange={(value: any) => setSessionType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dictation">Medical Dictation</SelectItem>
                      <SelectItem value="consultation">Patient Consultation</SelectItem>
                      <SelectItem value="notes">Clinical Notes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Patient Context</label>
                  <Select value={selectedPatient} onValueChange={setSelectedPatient}>
                    <SelectTrigger>
                      <SelectValue placeholder="No specific patient" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No specific patient</SelectItem>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.first_name} {patient.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Voice Synthesis Settings</h4>

                  <div className="space-y-2">
                    <label className="text-sm">Speech Rate: {voiceSettings.rate}</label>
                    <Slider
                      value={[voiceSettings.rate]}
                      onValueChange={([value]) => setVoiceSettings((prev) => ({ ...prev, rate: value }))}
                      min={0.5}
                      max={2}
                      step={0.1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm">Pitch: {voiceSettings.pitch}</label>
                    <Slider
                      value={[voiceSettings.pitch]}
                      onValueChange={([value]) => setVoiceSettings((prev) => ({ ...prev, pitch: value }))}
                      min={0.5}
                      max={2}
                      step={0.1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm">Volume: {voiceSettings.volume}</label>
                    <Slider
                      value={[voiceSettings.volume]}
                      onValueChange={([value]) => setVoiceSettings((prev) => ({ ...prev, volume: value }))}
                      min={0.1}
                      max={1}
                      step={0.1}
                      className="w-full"
                    />
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      speakText(
                        "Hello, this is a test of the voice synthesis with your current settings. How does this sound for medical consultations?",
                      )
                    }
                    className="w-full"
                  >
                    <Volume2 className="h-4 w-4 mr-2" />
                    Test Voice Settings
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="vapi" className="space-y-6">
        <TabsList>
          <TabsTrigger value="vapi">VAPI Voice Assistant</TabsTrigger>
          <TabsTrigger value="recorder">Voice Recorder</TabsTrigger>
          <TabsTrigger value="sessions">Session History</TabsTrigger>
        </TabsList>

        <TabsContent value="vapi" className="space-y-6">
          <VapiVoiceAssistant
            medicalContext={sessionType}
            patientId={selectedPatient !== "none" ? selectedPatient : undefined}
            onTranscriptUpdate={(transcript) => {
              setCurrentTranscript(transcript)
            }}
          />
        </TabsContent>

        <TabsContent value="recorder" className="space-y-6">
          {/* Voice Recorder Interface */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5" />
                Enhanced Voice Recorder
              </CardTitle>
              <CardDescription>
                Record medical dictations, consultations, and clinical notes with improved natural speech
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Session Configuration */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Session Type</label>
                  <Select
                    value={sessionType}
                    onValueChange={(value: any) => setSessionType(value)}
                    disabled={isRecording}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dictation">Medical Dictation</SelectItem>
                      <SelectItem value="consultation">Patient Consultation</SelectItem>
                      <SelectItem value="notes">Clinical Notes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Patient (Optional)</label>
                  <Select value={selectedPatient} onValueChange={setSelectedPatient} disabled={isRecording}>
                    <SelectTrigger>
                      <SelectValue placeholder="No specific patient" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No specific patient</SelectItem>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.first_name} {patient.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Recording Controls */}
              <div className="flex items-center justify-center gap-4">
                {!isRecording ? (
                  <Button size="lg" onClick={startRecording} className="px-8">
                    <Mic className="h-5 w-5 mr-2" />
                    Start Recording
                  </Button>
                ) : (
                  <>
                    <Button variant="outline" size="lg" onClick={togglePause}>
                      {isPaused ? <Play className="h-5 w-5 mr-2" /> : <Pause className="h-5 w-5 mr-2" />}
                      {isPaused ? "Resume" : "Pause"}
                    </Button>
                    <Button variant="destructive" size="lg" onClick={stopRecording}>
                      <Square className="h-5 w-5 mr-2" />
                      Stop Recording
                    </Button>
                    <Button variant="outline" size="lg" onClick={toggleMute}>
                      {isMuted ? <VolumeX className="h-5 w-5 mr-2" /> : <Volume2 className="h-5 w-5 mr-2" />}
                      {isMuted ? "Unmute" : "Mute"}
                    </Button>
                  </>
                )}
              </div>

              {/* Live Transcript */}
              {isRecording && (
                <Card className="border-green-200 dark:border-green-800">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      Live Transcript
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-32">
                      <p className="text-sm whitespace-pre-wrap">
                        {currentTranscript || "Listening... Start speaking to see transcript."}
                      </p>
                    </ScrollArea>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions */}
              <div className="grid grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  onClick={() =>
                    speakText(
                      "Recording started. Please begin your medical dictation. I'm ready to assist you with natural, professional voice feedback.",
                    )
                  }
                  disabled={isRecording}
                >
                  <Volume2 className="h-4 w-4 mr-2" />
                  Test Enhanced Voice
                </Button>
                <Button variant="outline" onClick={() => setCurrentTranscript("")} disabled={!isRecording}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Transcript
                </Button>
                <Button variant="outline" disabled>
                  <Download className="h-4 w-4 mr-2" />
                  Export Audio
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          {/* Session History */}
          <div className="grid gap-4">
            {voiceSessions.map((session) => (
              <Card key={session.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-accent">
                        {session.session_type === "dictation" && <FileText className="h-4 w-4" />}
                        {session.session_type === "consultation" && <User className="h-4 w-4" />}
                        {session.session_type === "notes" && <Bot className="h-4 w-4" />}
                      </div>
                      <div>
                        <CardTitle className="text-lg capitalize">{session.session_type} Session</CardTitle>
                        <CardDescription className="flex items-center gap-2">
                          <Badge
                            className={
                              session.status === "completed"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : session.status === "active"
                                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                  : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                            }
                          >
                            {session.status}
                          </Badge>
                          {session.patient && (
                            <span className="text-sm">
                              {session.patient.first_name} {session.patient.last_name}
                            </span>
                          )}
                        </CardDescription>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => deleteSession(session.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatTime(session.duration_seconds)}
                    </div>
                    <div>{new Date(session.created_at).toLocaleDateString()}</div>
                    <div>{new Date(session.created_at).toLocaleTimeString()}</div>
                  </div>
                  {session.transcript && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Transcript:</h4>
                      <ScrollArea className="h-20">
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">{session.transcript}</p>
                      </ScrollArea>
                    </div>
                  )}
                  {session.summary && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">AI Summary:</h4>
                      <p className="text-sm text-muted-foreground">{session.summary}</p>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" disabled>
                      <Play className="h-4 w-4 mr-1" />
                      Play Audio
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => session.transcript && speakText(session.transcript)}
                      disabled={!session.transcript}
                    >
                      <Volume2 className="h-4 w-4 mr-1" />
                      Read Aloud
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {voiceSessions.length === 0 && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Mic className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No voice sessions yet</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Start your first voice recording session to see it here
                </p>
                <Button onClick={() => document.querySelector('[value="recorder"]')?.click()}>
                  <Mic className="h-4 w-4 mr-2" />
                  Start Recording
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
