import { tool, type UIMessage } from "ai"
import { z } from "zod"
import { createClient } from "@/lib/supabase/server"
import { API_CONFIG } from "@/lib/config"

export const maxDuration = 30

// Medical tools for the AI assistant
const getPatientInfoTool = tool({
  description: "Get patient information by ID for medical consultation",
  inputSchema: z.object({
    patientId: z.string().describe("The patient ID to look up"),
  }),
  execute: async ({ patientId }) => {
    const supabase = await createClient()

    const { data: patient, error } = await supabase.from("patients").select("*").eq("id", patientId).single()

    if (error || !patient) {
      return { error: "Patient not found" }
    }

    return {
      name: `${patient.first_name} ${patient.last_name}`,
      age: patient.date_of_birth ? calculateAge(patient.date_of_birth) : "Unknown",
      gender: patient.gender || "Not specified",
      medicalHistory: patient.medical_history || "No medical history recorded",
      allergies: patient.allergies || "No known allergies",
      currentMedications: patient.current_medications || "No current medications",
    }
  },
})

const getMedicalRecordsTool = tool({
  description: "Get recent medical records for a patient",
  inputSchema: z.object({
    patientId: z.string().describe("The patient ID to get records for"),
    limit: z.number().optional().describe("Number of records to retrieve (default: 5)"),
  }),
  execute: async ({ patientId, limit = 5 }) => {
    const supabase = await createClient()

    const { data: records, error } = await supabase
      .from("medical_records")
      .select("*")
      .eq("patient_id", patientId)
      .order("created_at", { ascending: false })
      .limit(limit)

    if (error) {
      return { error: "Failed to retrieve medical records" }
    }

    return {
      records:
        records?.map((record) => ({
          date: record.created_at,
          type: record.record_type,
          title: record.title,
          content: record.content,
          diagnosis: record.diagnosis,
          treatmentPlan: record.treatment_plan,
        })) || [],
    }
  },
})

const getAppointmentsTool = tool({
  description: "Get upcoming appointments for scheduling and planning",
  inputSchema: z.object({
    days: z.number().optional().describe("Number of days ahead to look (default: 7)"),
  }),
  execute: async ({ days = 7 }) => {
    const supabase = await createClient()
    const { data: userData } = await supabase.auth.getUser()

    if (!userData.user) {
      return { error: "Not authenticated" }
    }

    const startDate = new Date()
    const endDate = new Date()
    endDate.setDate(endDate.getDate() + days)

    const { data: appointments, error } = await supabase
      .from("appointments")
      .select(`
        *,
        patient:patients(first_name, last_name)
      `)
      .eq("doctor_id", userData.user.id)
      .gte("appointment_date", startDate.toISOString())
      .lte("appointment_date", endDate.toISOString())
      .order("appointment_date", { ascending: true })

    if (error) {
      return { error: "Failed to retrieve appointments" }
    }

    return {
      appointments:
        appointments?.map((apt) => ({
          date: apt.appointment_date,
          patient: `${apt.patient.first_name} ${apt.patient.last_name}`,
          title: apt.title,
          type: apt.appointment_type,
          status: apt.status,
        })) || [],
    }
  },
})

const medicalReferenceTool = tool({
  description: "Provide medical reference information, drug interactions, or clinical guidelines",
  inputSchema: z.object({
    query: z.string().describe("Medical question or topic to research"),
    category: z.enum(["symptoms", "diagnosis", "treatment", "medications", "procedures"]).optional(),
  }),
  execute: async ({ query, category }) => {
    // In a real implementation, this would query medical databases
    // For now, we'll provide general guidance
    return {
      query,
      category: category || "general",
      guidance:
        "This is a simulated medical reference response. In a production environment, this would connect to verified medical databases and clinical decision support systems.",
      disclaimer: "Always verify information with current medical literature and consult with specialists when needed.",
    }
  },
})

const tools = {
  getPatientInfo: getPatientInfoTool,
  getMedicalRecords: getMedicalRecordsTool,
  getAppointments: getAppointmentsTool,
  medicalReference: medicalReferenceTool,
}

function calculateAge(dateOfBirth: string): number {
  const today = new Date()
  const birthDate = new Date(dateOfBirth)
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  return age
}

export async function POST(req: Request) {
  const { messages }: { messages: UIMessage[] } = await req.json()

  try {
    // Get the last user message
    const lastMessage = messages[messages.length - 1]
    if (!lastMessage || lastMessage.role !== "user") {
      return new Response("Invalid message format", { status: 400 })
    }

    const response = await fetch(API_CONFIG.GEMINI_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message: lastMessage.content,
        max_tokens: 512,
      }),
    })

    console.log("[v0] Chatbot API response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("[v0] Chatbot API error response:", errorText)
      throw new Error(`Chatbot API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    console.log("[v0] Chatbot API response data:", data)

    // Return the response in the expected format
    return new Response(
      JSON.stringify({
        role: "assistant",
        content: data.response || "I apologize, but I encountered an issue processing your request. Please try again.",
        model: data.model || "gemini",
      }),
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  } catch (error) {
    console.error("AI Assistant error:", error)
    return new Response(
      JSON.stringify({
        role: "assistant",
        content: "I apologize, but I encountered a technical issue. Please try again later.",
        error: true,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}
