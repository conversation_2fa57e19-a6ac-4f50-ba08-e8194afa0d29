// API Configuration for external services
export const API_CONFIG = {
  // Gemini AI Assistant
  GEMINI_API_URL: "https://gemini-proxy-modifs.onrender.com/chat",

  // Agora Video Calling (public app ID is safe to expose)
  AGORA_APP_ID: process.env.NEXT_PUBLIC_AGORA_APP_ID || "ae6da607a64c445ea073b08ca2c88e42",
} as const

// Client-side environment validation (only for public variables)
export function validateClientEnvironment() {
  const missing: string[] = []

  if (!API_CONFIG.AGORA_APP_ID) {
    missing.push("NEXT_PUBLIC_AGORA_APP_ID")
  }

  return missing
}

// API status check (client-safe version)
export async function checkAPIStatus() {
  const status = {
    gemini: false,
    agora: !!API_CONFIG.AGORA_APP_ID,
  }

  // Test Gemini API
  try {
    const response = await fetch(API_CONFIG.GEMINI_API_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ message: "test", max_tokens: 10 }),
    })
    status.gemini = response.ok
  } catch {
    status.gemini = false
  }

  return status
}
