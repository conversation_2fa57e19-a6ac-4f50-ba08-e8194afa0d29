-- Insert sample patients (this will only work after a doctor is authenticated)
-- This is just for reference - actual data will be inserted through the app

-- Sample patient data structure for reference:
/*
INSERT INTO public.patients (
  doctor_id,
  first_name,
  last_name,
  email,
  phone,
  date_of_birth,
  gender,
  address,
  emergency_contact_name,
  emergency_contact_phone,
  medical_history,
  allergies,
  current_medications
) VALUES (
  auth.uid(), -- This will be the authenticated doctor's ID
  '<PERSON>',
  'Doe',
  '<EMAIL>',
  '******-0123',
  '1985-06-15',
  'Male',
  '123 Main St, City, State 12345',
  '<PERSON>',
  '******-0124',
  'Hypertension, Type 2 Diabetes',
  'Penicillin, Shellfish',
  'Metformin 500mg twice daily, Lisinopril 10mg once daily'
);
*/

-- Note: Actual sample data will be inserted through the application
-- after authentication is set up to ensure proper RLS compliance
